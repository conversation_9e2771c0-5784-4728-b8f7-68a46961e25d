<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title x-text="pageTitle">Student Management System</title>
    
    <!-- Material Design CSS -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/material-components-web/14.0.0/material-components-web.min.css" rel="stylesheet">
    
    <!-- Material Icons -->
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">

    <!-- Flatpickr CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">

    <!-- HTML5 QR Code Scanner CSS -->
    <style>
        #qr-reader {
            width: 100%;
            max-width: 500px;
            margin: 0 auto;
        }

        #qr-reader__dashboard_section_csr {
            display: none !important;
        }

        #qr-reader__camera_selection {
            margin-bottom: 16px;
        }

        #qr-reader__scan_region {
            border-radius: 8px;
            overflow: hidden;
        }

        #qr-reader__scan_region video {
            border-radius: 8px;
        }
    </style>

    <!-- Custom CSS -->
    <link rel="stylesheet" href="styles.css">
</head>
<body x-data="appData()"
      x-init="initializeApp()"
      :class="{ 'dark-mode': darkMode, 'login-page': currentPage === 'login' }">

    <!-- Page Preloader -->
    <div class="page-preloader" id="page-preloader" x-show="isLoading">
        <div class="preloader-content">
            <div class="mdc-circular-progress mdc-circular-progress--large mdc-circular-progress--indeterminate" role="progressbar" aria-label="Loading..." aria-valuemin="0" aria-valuemax="1">
                <div class="mdc-circular-progress__determinate-container">
                    <svg class="mdc-circular-progress__determinate-circle-graphic" viewBox="0 0 48 48" xmlns="http://www.w3.org/2000/svg">
                        <circle class="mdc-circular-progress__determinate-track" cx="24" cy="24" r="18" stroke-width="4"/>
                        <circle class="mdc-circular-progress__determinate-circle" cx="24" cy="24" r="18" stroke-dasharray="113.097" stroke-dashoffset="113.097" stroke-width="4"/>
                    </svg>
                </div>
                <div class="mdc-circular-progress__indeterminate-container">
                    <div class="mdc-circular-progress__spinner-layer">
                        <div class="mdc-circular-progress__circle-clipper mdc-circular-progress__circle-left">
                            <svg class="mdc-circular-progress__indeterminate-circle-graphic" viewBox="0 0 48 48" xmlns="http://www.w3.org/2000/svg">
                                <circle cx="24" cy="24" r="18" stroke-dasharray="113.097" stroke-dashoffset="56.549" stroke-width="4"/>
                            </svg>
                        </div>
                        <div class="mdc-circular-progress__gap-patch">
                            <svg class="mdc-circular-progress__indeterminate-circle-graphic" viewBox="0 0 48 48" xmlns="http://www.w3.org/2000/svg">
                                <circle cx="24" cy="24" r="18" stroke-dasharray="113.097" stroke-dashoffset="56.549" stroke-width="3.2"/>
                            </svg>
                        </div>
                        <div class="mdc-circular-progress__circle-clipper mdc-circular-progress__circle-right">
                            <svg class="mdc-circular-progress__indeterminate-circle-graphic" viewBox="0 0 48 48" xmlns="http://www.w3.org/2000/svg">
                                <circle cx="24" cy="24" r="18" stroke-dasharray="113.097" stroke-dashoffset="56.549" stroke-width="4"/>
                            </svg>
                        </div>
                    </div>
                </div>
            </div>
            <div class="preloader-text" x-text="loadingText">Loading...</div>
        </div>
    </div>

    <!-- Loading Overlay for UI Interactions -->
    <div class="loading-overlay" id="loading-overlay" x-show="showLoadingOverlay">
        <div class="loading-content">
            <div class="mdc-circular-progress mdc-circular-progress--medium" role="progressbar" aria-label="Processing..." aria-valuemin="0" aria-valuemax="1">
                <div class="mdc-circular-progress__determinate-container">
                    <svg class="mdc-circular-progress__determinate-circle-graphic" viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg">
                        <circle class="mdc-circular-progress__determinate-track" cx="16" cy="16" r="12.5" stroke-width="3"/>
                        <circle class="mdc-circular-progress__determinate-circle" cx="16" cy="16" r="12.5" stroke-dasharray="78.54" stroke-dashoffset="78.54" stroke-width="3"/>
                    </svg>
                </div>
                <div class="mdc-circular-progress__indeterminate-container">
                    <div class="mdc-circular-progress__spinner-layer">
                        <div class="mdc-circular-progress__circle-clipper mdc-circular-progress__circle-left">
                            <svg class="mdc-circular-progress__indeterminate-circle-graphic" viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg">
                                <circle cx="16" cy="16" r="12.5" stroke-dasharray="78.54" stroke-dashoffset="39.27" stroke-width="3"/>
                            </svg>
                        </div>
                        <div class="mdc-circular-progress__gap-patch">
                            <svg class="mdc-circular-progress__indeterminate-circle-graphic" viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg">
                                <circle cx="16" cy="16" r="12.5" stroke-dasharray="78.54" stroke-dashoffset="39.27" stroke-width="2.4"/>
                            </svg>
                        </div>
                        <div class="mdc-circular-progress__circle-clipper mdc-circular-progress__circle-right">
                            <svg class="mdc-circular-progress__indeterminate-circle-graphic" viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg">
                                <circle cx="16" cy="16" r="12.5" stroke-dasharray="78.54" stroke-dashoffset="39.27" stroke-width="3"/>
                            </svg>
                        </div>
                    </div>
                </div>
            </div>
            <div class="loading-text" x-text="overlayLoadingText">Processing...</div>
        </div>
    </div>

    <!-- App Bar (Hidden on login page) -->
    <div class="app-bar" x-show="currentPage !== 'login'" x-transition>
        <span class="material-icons" id="menu-btn" @click="toggleSidebar()">menu</span>
        <h1 x-text="appBarTitle">Student Management System</h1>
        <div class="actions">
            <span class="material-icons" id="dark-mode-toggle" title="Toggle Dark Mode" @click="toggleDarkMode()">dark_mode</span>
            <span class="material-icons" id="more-menu-btn">more_vert</span>
        </div>
    </div>
    
    <!-- Sidebar (Hidden on login page) -->
    <div class="sidebar" id="sidebar" x-show="currentPage !== 'login'" x-transition :class="{ 'sidebar-open': sidebarOpen }">
        <div class="sidebar-section">
            <a href="#" class="sidebar-item" :class="{ 'active': currentPage === 'home' }" @click="navigateTo('home')">
                <span class="material-icons">home</span>
                <span>Home</span>
            </a>
            <a href="#" class="sidebar-item" :class="{ 'active': currentPage === 'students' }" @click="navigateTo('students')">
                <span class="material-icons">people</span>
                <span>Students</span>
            </a>
            <div class="sidebar-item" id="grades-menu" @click="toggleGradesMenu()">
                <span class="material-icons">grade</span>
                <span>Grades</span>
                <span class="material-icons expand-icon" :class="{ 'expanded': gradesMenuOpen }">expand_more</span>
            </div>
            <div class="submenu" id="grades-submenu" x-show="gradesMenuOpen" x-transition>
                <a href="#" class="submenu-item" @click="navigateTo('grades-list')">Grades List</a>
                <a href="#" class="submenu-item" @click="navigateTo('subjects')">Subjects</a>
            </div>
        </div>
    </div>
    
    <!-- Main Content Container -->
    <div class="main-content" :class="{ 'sidebar-open': sidebarOpen && currentPage !== 'login' }">
        <!-- Dynamic Template Content -->
        <div id="template-container" x-html="templateContent">
            <!-- Templates will be loaded here dynamically -->
        </div>
    </div>

    <!-- Bottom App Bar (Mobile) - Hidden on login page -->
    <div class="bottom-app-bar" id="bottom-app-bar" x-show="currentPage !== 'login'" x-transition>
        <div class="bottom-nav-item" :class="{ 'active': currentPage === 'home' }" @click="navigateTo('home')">
            <span class="material-icons">home</span>
            <span class="bottom-nav-label">Home</span>
        </div>
        <div class="bottom-nav-item" :class="{ 'active': currentPage === 'students' }" @click="navigateTo('students')">
            <span class="material-icons">people</span>
            <span class="bottom-nav-label">Students</span>
        </div>
        <div class="bottom-nav-item" :class="{ 'active': currentPage === 'stats' }" @click="navigateTo('stats')">
            <span class="material-icons">analytics</span>
            <span class="bottom-nav-label">Stats</span>
        </div>
        <div class="bottom-nav-item" :class="{ 'active': currentPage === 'attendance' }" @click="navigateTo('attendance')">
            <span class="material-icons">event_available</span>
            <span class="bottom-nav-label">Attendance</span>
        </div>
    </div>

    <!-- QR Code Floating Action Button - Hidden on login page -->
    <button class="mdc-fab qr-fab" id="qr-fab" x-show="currentPage !== 'login'" x-transition>
        <div class="mdc-fab__ripple"></div>
        <span class="material-icons mdc-fab__icon">qr_code</span>
    </button>

    <!-- Add Student Floating Action Button - Show only on students page -->
    <button class="mdc-fab mdc-fab--extended" id="add-student-fab" x-show="currentPage === 'students'" x-transition>
        <div class="mdc-fab__ripple"></div>
        <span class="material-icons mdc-fab__icon">add</span>
        <span class="mdc-fab__label">Add Student</span>
    </button>

    <!-- Shared Components Container -->
    <div id="shared-components">
        <!-- Shared components like modals, bottom sheets, etc. will be loaded here -->
    </div>

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    
    <!-- Alpine.js -->
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>
    
    <!-- Material Design Components JS -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/material-components-web/14.0.0/material-components-web.min.js"></script>
    
    <!-- Flatpickr JS -->
    <script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
    
    <!-- HTML5 QR Code Scanner -->
    <script src="https://unpkg.com/html5-qrcode" type="text/javascript"></script>
    
    <!-- Custom JS Modules -->
    <script src="js/modules/template-loader.js"></script>
    <script src="js/modules/navigation.js"></script>
    <script src="js/app.js"></script>
</body>
</html>
